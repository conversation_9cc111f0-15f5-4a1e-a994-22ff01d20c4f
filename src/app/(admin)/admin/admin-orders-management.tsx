'use client';

import type { LucideIcon } from 'lucide-react';
import {
  <PERSON><PERSON>hart3,
  CheckCircle,
  DollarSign,
  Gift,
  Plus,
  RefreshCw,
  ShoppingCart,
  Trash2,
  XCircle,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import type { AdminOrderStats } from '@/api/admin-api';
import {
  deleteOrder,
  getAdminOrders,
  getAdminOrderStats,
} from '@/api/admin-api';
import { cancelOrder } from '@/api/order-api';
import { getUserById } from '@/api/user-api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { OrderEntity, UserEntity } from '@/constants/core.constants';
import { OrderStatus, UserType } from '@/constants/core.constants';
import { useToast } from '@/hooks/use-toast';
import { useRootContext } from '@/root-context';

interface StatCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  description: string;
}

function StatCard({ title, value, icon: Icon, description }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}

export function AdminOrdersManagement() {
  const { toast } = useToast();
  const { currentUser } = useRootContext();
  const [stats, setStats] = useState<AdminOrderStats | null>(null);
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingStats, setLoadingStats] = useState(true);
  const [userCache, setUserCache] = useState<Record<string, UserEntity>>({});

  const loadStats = useCallback(async () => {
    try {
      setLoadingStats(true);
      const statsData = await getAdminOrderStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading admin order stats:', error);
      toast({
        title: 'Error',
        description: 'Failed to load order statistics',
        variant: 'destructive',
      });
    } finally {
      setLoadingStats(false);
    }
  }, [toast]);

  const loadOrders = useCallback(async () => {
    try {
      setLoading(true);
      const result = await getAdminOrders(25);
      setOrders(result.orders);

      const userIds = new Set<string>();
      result.orders.forEach((order) => {
        if (order.buyerId) userIds.add(order.buyerId);
        if (order.sellerId) userIds.add(order.sellerId);
      });

      const userPromises = Array.from(userIds).map(async (userId) => {
        if (!userCache[userId]) {
          try {
            const user = await getUserById(userId);
            return { userId, user };
          } catch (error) {
            console.error(`Error loading user ${userId}:`, error);
            return { userId, user: null };
          }
        }
        return { userId, user: userCache[userId] };
      });

      const userResults = await Promise.all(userPromises);
      const newUserCache = { ...userCache };
      userResults.forEach(({ userId, user }) => {
        if (user) {
          newUserCache[userId] = user;
        }
      });
      setUserCache(newUserCache);
    } catch (error) {
      console.error('Error loading admin orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to load orders',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast, userCache]);

  const handleCancelOrder = async (orderId: string) => {
    if (!currentUser?.id) return;

    try {
      await cancelOrder(orderId, currentUser.id);
      toast({
        title: 'Success',
        description: 'Order cancelled successfully',
      });
      await loadOrders();
      await loadStats();
    } catch (error) {
      console.error('Error cancelling order:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel order',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteOrder = async (orderId: string) => {
    try {
      await deleteOrder(orderId);
      toast({
        title: 'Success',
        description: 'Order deleted successfully',
      });
      await loadOrders();
      await loadStats();
    } catch (error) {
      console.error('Error deleting order:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete order',
        variant: 'destructive',
      });
    }
  };

  const getAdminUserType = (order: OrderEntity): UserType | null => {
    const buyerIsAdmin =
      order.buyerId && userCache[order.buyerId]?.role === 'admin';
    const sellerIsAdmin =
      order.sellerId && userCache[order.sellerId]?.role === 'admin';

    if (buyerIsAdmin && sellerIsAdmin) {
      return UserType.BUYER;
    }
    if (buyerIsAdmin) return UserType.BUYER;
    if (sellerIsAdmin) return UserType.SELLER;
    return null;
  };

  const formatPrice = (price: number) => `${price.toFixed(2)} TON`;

  useEffect(() => {
    loadStats();
    loadOrders();
  }, [loadStats, loadOrders]);

  if (loadingStats || loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {stats && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <StatCard
              title="Total Orders"
              value={stats.totalOrders}
              icon={BarChart3}
              description="All orders in system"
            />
            <StatCard
              title="Non-Admin Orders"
              value={
                stats.nonAdminOrders.active +
                stats.nonAdminOrders.paid +
                stats.nonAdminOrders.giftSentToRelayer +
                stats.nonAdminOrders.cancelled +
                stats.nonAdminOrders.fulfilled
              }
              icon={ShoppingCart}
              description="Orders without admin involvement"
            />
            <StatCard
              title="Admin Orders"
              value={
                stats.adminOrders.active +
                stats.adminOrders.paid +
                stats.adminOrders.giftSentToRelayer +
                stats.adminOrders.cancelled +
                stats.adminOrders.fulfilled
              }
              icon={DollarSign}
              description="Orders with admin involvement"
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <StatCard
              title="Active (Non-Admin)"
              value={stats.nonAdminOrders.active}
              icon={RefreshCw}
              description="Active orders without admins"
            />
            <StatCard
              title="Paid (Non-Admin)"
              value={stats.nonAdminOrders.paid}
              icon={DollarSign}
              description="Paid orders without admins"
            />
            <StatCard
              title="Gift Sent (Non-Admin)"
              value={stats.nonAdminOrders.giftSentToRelayer}
              icon={Gift}
              description="Gift sent without admins"
            />
            <StatCard
              title="Cancelled (Non-Admin)"
              value={stats.nonAdminOrders.cancelled}
              icon={XCircle}
              description="Cancelled orders without admins"
            />
            <StatCard
              title="Fulfilled (Non-Admin)"
              value={stats.nonAdminOrders.fulfilled}
              icon={CheckCircle}
              description="Fulfilled orders without admins"
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <StatCard
              title="Active (Admin)"
              value={stats.adminOrders.active}
              icon={RefreshCw}
              description="Active orders with admins"
            />
            <StatCard
              title="Paid (Admin)"
              value={stats.adminOrders.paid}
              icon={DollarSign}
              description="Paid orders with admins"
            />
            <StatCard
              title="Gift Sent (Admin)"
              value={stats.adminOrders.giftSentToRelayer}
              icon={Gift}
              description="Gift sent with admins"
            />
            <StatCard
              title="Cancelled (Admin)"
              value={stats.adminOrders.cancelled}
              icon={XCircle}
              description="Cancelled orders with admins"
            />
            <StatCard
              title="Fulfilled (Admin)"
              value={stats.adminOrders.fulfilled}
              icon={CheckCircle}
              description="Fulfilled orders with admins"
            />
          </div>
        </div>
      )}

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Admin Orders</h3>
          <Button disabled>
            <Plus className="h-4 w-4 mr-2" />
            Create Admin Order
          </Button>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order Number</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Admin Type</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Secondary Price</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => {
                const adminType = getAdminUserType(order);
                return (
                  <TableRow key={order.id}>
                    <TableCell className="font-mono">#{order.number}</TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          order.status === OrderStatus.ACTIVE
                            ? 'bg-blue-100 text-blue-800'
                            : order.status === OrderStatus.PAID
                              ? 'bg-green-100 text-green-800'
                              : order.status ===
                                  OrderStatus.GIFT_SENT_TO_RELAYER
                                ? 'bg-purple-100 text-purple-800'
                                : order.status === OrderStatus.FULFILLED
                                  ? 'bg-emerald-100 text-emerald-800'
                                  : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {order.status}
                      </span>
                    </TableCell>
                    <TableCell>{adminType || 'N/A'}</TableCell>
                    <TableCell>{formatPrice(order.price)}</TableCell>
                    <TableCell>
                      {order.secondaryMarketPrice
                        ? formatPrice(order.secondaryMarketPrice)
                        : '-'}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {order.status === OrderStatus.CANCELLED ? (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                                Delete
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Delete Order
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to permanently delete
                                  order #{order.number}? This action cannot be
                                  undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteOrder(order.id!)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        ) : (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <XCircle className="h-4 w-4" />
                                Cancel
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Cancel Order
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to cancel order #
                                  {order.number}? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleCancelOrder(order.id!)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Cancel Order
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
